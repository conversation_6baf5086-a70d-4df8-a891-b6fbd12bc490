<?php

namespace App\Services;

use Exception;
use Stripe\Stripe;
use Stripe\PaymentIntent;
use Stripe\Customer;
use Stripe\PaymentMethod;
use Illuminate\Support\Facades\Log;

class StripeService
{
    public function __construct()
    {
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    /**
     * Process payment with Stripe API
     */
    public function processStripePayment($paymentMethodId, $orderData, $clubs, $coupon = null)
    {
        try {
            // Validate payment method ID
            if (empty($paymentMethodId)) {
                return [
                    'success' => false,
                    'message' => 'Payment method is required'
                ];
            }

            // Validate API key
            if (empty(config('services.stripe.secret'))) {
                return [
                    'success' => false,
                    'message' => 'Stripe configuration error: API key not set'
                ];
            }

            Log::info('Starting Stripe Payment Process', [
                'payment_method_id' => $paymentMethodId,
                'has_api_key' => !empty(config('services.stripe.secret'))
            ]);

            $paymentData = $this->preparePaymentData($paymentMethodId, $orderData, $clubs, $coupon);
            $paymentIntent = $this->createPaymentIntent($paymentData);

            return $this->handleStripeResponse($paymentIntent);

        } catch (Exception $e) {
            Log::error('Stripe Payment Exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Payment processing error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Prepare payment data for Stripe API
     */
    private function preparePaymentData($paymentMethodId, $orderData, $clubs, $coupon)
    {
        $order = (object) $orderData;

        // Calculate subtotal from clubs data (before any discounts)
        $subtotal = 0;
        if (is_array($clubs) && !empty($clubs)) {
            $subtotal = $this->calculateSubtotalFromClubs($clubs);
        }

        // If no clubs or calculation failed, get original total before discount
        if ($subtotal <= 0) {
            // If coupon is applied, get original total before discount
            if ($coupon && isset($coupon['original_total'])) {
                $subtotal = $coupon['original_total'];
            } else {
                $subtotal = $order->TtlAmt ?? 0;
            }
        }

        // Calculate discount from original subtotal
        $discountAmount = $this->calculateDiscount($subtotal, $coupon);
        $tax = $this->calculateTax($orderData);
        $shippingAmount = $this->calculateShipping($orderData);

        // Calculate final total
        $total = $subtotal - $discountAmount + $tax + $shippingAmount;

        // Ensure minimum total amount (Stripe requires minimum $0.50)
        if ($total < 0.50) {
            $total = 0.50;
        }

        // Convert to cents for Stripe (Stripe expects amounts in cents)
        $amountInCents = round($total * 100);

        return [
            'payment_method_id' => $paymentMethodId,
            'amount' => $amountInCents,
            'currency' => 'usd',
            'description' => $this->buildPaymentDescription($order, $clubs),
            'metadata' => [
                'order_number' => $order->OrdNo ?? 'ORD-' . time(),
                'subtotal' => $subtotal,
                'discount' => $discountAmount,
                'tax' => $tax,
                'shipping' => $shippingAmount,
                'total' => $total
            ]
        ];
    }

    /**
     * Create Stripe Payment Intent
     */
    private function createPaymentIntent($paymentData)
    {
        return PaymentIntent::create([
            'amount' => $paymentData['amount'],
            'currency' => $paymentData['currency'],
            'payment_method' => $paymentData['payment_method_id'],
            'description' => $paymentData['description'],
            'metadata' => $paymentData['metadata'],
            'confirm' => true,
            'return_url' => url('/payment/success'), // You may need to adjust this URL
        ]);
    }

    /**
     * Handle Stripe response
     */
    private function handleStripeResponse($paymentIntent)
    {
        if ($paymentIntent->status === 'succeeded') {
            Log::info('Stripe Payment Successful', [
                'payment_intent_id' => $paymentIntent->id,
                'amount' => $paymentIntent->amount,
                'status' => $paymentIntent->status
            ]);

            return [
                'success' => true,
                'message' => 'Payment processed successfully',
                'transaction_id' => $paymentIntent->id,
                'amount' => $paymentIntent->amount / 100, // Convert back to dollars
                'status' => $paymentIntent->status,
                'payment_method' => $paymentIntent->payment_method
            ];
        } else {
            Log::warning('Stripe Payment Failed', [
                'payment_intent_id' => $paymentIntent->id,
                'status' => $paymentIntent->status,
                'last_payment_error' => $paymentIntent->last_payment_error
            ]);

            return [
                'success' => false,
                'message' => 'Payment failed: ' . ($paymentIntent->last_payment_error->message ?? 'Unknown error'),
                'transaction_id' => $paymentIntent->id,
                'status' => $paymentIntent->status
            ];
        }
    }

    /**
     * Calculate subtotal from clubs data
     */
    private function calculateSubtotalFromClubs($clubs)
    {
        $subtotal = 0;
        foreach ($clubs as $club) {
            if (isset($club['price']) && isset($club['quantity'])) {
                $subtotal += (float)$club['price'] * (int)$club['quantity'];
            }
        }
        return $subtotal;
    }

    /**
     * Calculate discount amount
     */
    private function calculateDiscount($subtotal, $coupon)
    {
        if (!$coupon || !isset($coupon['discount_amount'])) {
            return 0;
        }
        return (float)$coupon['discount_amount'];
    }

    /**
     * Calculate tax amount
     */
    private function calculateTax($orderData)
    {
        $order = (object) $orderData;
        return (float)($order->TaxAmt ?? 0);
    }

    /**
     * Calculate shipping amount
     */
    private function calculateShipping($orderData)
    {
        $order = (object) $orderData;
        return (float)($order->ShipAmt ?? 0);
    }

    /**
     * Build payment description
     */
    private function buildPaymentDescription($order, $clubs)
    {
        $description = 'Golf Club Order';
        if (isset($order->OrdNo)) {
            $description .= ' - Order #' . $order->OrdNo;
        }
        if (is_array($clubs) && count($clubs) > 0) {
            $description .= ' (' . count($clubs) . ' clubs)';
        }
        return $description;
    }

    /**
     * Create Stripe customer
     */
    public function createCustomer($customerData)
    {
        try {
            $customer = Customer::create([
                'name' => $customerData['name'] ?? '',
                'email' => $customerData['email'] ?? '',
                'phone' => $customerData['phone'] ?? '',
                'address' => [
                    'line1' => $customerData['address_line1'] ?? '',
                    'line2' => $customerData['address_line2'] ?? '',
                    'city' => $customerData['city'] ?? '',
                    'state' => $customerData['state'] ?? '',
                    'postal_code' => $customerData['postal_code'] ?? '',
                    'country' => $customerData['country'] ?? '',
                ],
                'metadata' => $customerData['metadata'] ?? []
            ]);

            return [
                'success' => true,
                'customer_id' => $customer->id,
                'data' => $customer
            ];
        } catch (Exception $e) {
            Log::error('Stripe Customer Creation Failed', [
                'error' => $e->getMessage(),
                'customer_data' => $customerData
            ]);

            return [
                'success' => false,
                'message' => 'Failed to create customer: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Add payment method to customer
     */
    public function addPaymentMethod($customerId, $paymentMethodId)
    {
        try {
            $paymentMethod = PaymentMethod::retrieve($paymentMethodId);
            $paymentMethod->attach(['customer' => $customerId]);

            return [
                'success' => true,
                'payment_method' => $paymentMethod
            ];
        } catch (Exception $e) {
            Log::error('Stripe Payment Method Attachment Failed', [
                'error' => $e->getMessage(),
                'customer_id' => $customerId,
                'payment_method_id' => $paymentMethodId
            ]);

            return [
                'success' => false,
                'message' => 'Failed to attach payment method: ' . $e->getMessage()
            ];
        }
    }
}
